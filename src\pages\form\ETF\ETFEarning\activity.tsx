import React, {useEffect, useState} from 'react'
import FormRender from 'form-render/lib/antd';
import {Button, Popconfirm, message, Upload} from 'antd';
// import UploadImg from './components/UploadImg';
import UploadImg from '../../components/uploadFile';
import AwardTable from './awardTable/index';
import getSchema from './config'
import api from 'api';
import styles from './index.less';
const SCHEMA1 = getSchema(false);
const SCHEMA2 = getSchema(true);

interface ETFActivityItem {
    "activityId": string
    "type": string
    "cardType": string
    "activityName": string
    "startDate": string
    "endDate": string
    "status": string
    "title": string
    "subTitle": string
    "tipTitle": string
    "tipContext": string
    "recommendTitle": string
    "recommendCode": string
    "recommendName": string
    "profitDescribe": string
    "productTitle": string
    "purchaseContext": string
    "windowText": string
    "activityRules": string | any
    "codeRate": string
    "cashVoucherList": any
}
// postEtfEarningConfig: 'post /common_config/kv_data_save?key=etf-earning',
// getEtfEarningConfig: 'get /common_config/kv_data_get?key=etf-earning',
// uploadEarningBonus: 'post /yytjapi/awardfile/v1/upload',
// getUnusedAward: 'get /awardfile/v1/unused',
// getUsedAward: 'get /awardfile/v1/used',

const {
    fetchETFActivity,
    uploadETFActivityFile,
    fetchETFActivityAward,
		postEtfEarningConfig
} = api

export default function({
    isAdd = false,
    isShowAdd,
    activities = [],
    activity,
    setActivity,
		awardList,
		setAwardList,
    setIsShowAdd = () => {},
    handleFetchETFActivity = () => {}
}: {
    isAdd?: boolean
    isShowAdd: boolean
    activities: any
    activity?: any
    setActivity: any
		awardList: any,
		setAwardList: Function,
    setIsShowAdd: Function
    handleFetchETFActivity: Function
}) {

    // const [formData, setData] = useState({});
    const [valid, setValid] = useState([]);
    const [file, setFile] = useState<File | null>(null)

    useEffect(() => {
        if (!isShowAdd) {
            setFile(null)
        }
    }, [isShowAdd])

    /**
     * 提交
     */
    function postConfig() {
				console.log(activity, activities, valid, 'activity')
        if (activities.filter((item) => item.activeId === activity.activeId).length > (isAdd ? 0 : 1)) return message.error('activeId重复，需为唯一')
        if (valid.length > 0) return message.error('必填项未填');

        if (activity.activityRules.length === 0) return message.error('请填写规则');
				const item = awardList.filter(item => item.coupon && item.url);
				if (item && item.length > 0) {
					message.error('奖品码和领取奖励不能同时存在');
					return;
				}
        let _form: any = {...activity}
        // _form.activityRules = JSON.stringify(_form.activityRules)
        // _form.recommend = JSON.stringify(_form.recommend)
        // _form.product = JSON.stringify(_form.product)
				// _form.award = JSON.stringify(awardList);
				_form.award = awardList;
				// console.log(_form, awardList, '_form');
				// _.fundLoading()
				// let formData;
				// if (isAdd) {
				// 	const list = [...activities];
				// 	list.push(_form);
				// 	formData = list;
				// } else {
				// 	const index = activities.findIndex(item => item.activeId === activity.activeId);
				// 	const list = [...activities];
				// 	list.splice(index, 1, _form);
				// 	formData = list;
				// }
				console.log(JSON.stringify(_form), _form, 'JSON.stringify(formData)')
        postEtfEarningConfig({
						key: 'etf-earning-new',
						propName: _form.activeId,
            value: JSON.stringify(_form)
        }).then((res: any) => {
						console.log(res, 'res');
            _.hideFundLoading()
            if (res.code === '0000') {
                setIsShowAdd(false)
                message.success(isAdd ? '添加完成' : '修改完成');
                handleFetchETFActivity();
            }
        }).catch(() => {
            _.hideFundLoading()
        })
    }


		const LogoUpload = ({ value, onChange, name}) => {
			console.log(value, name);
			const uploadCallback = (fileName: string, size: number, url: string) => {
				onChange(name, url);
			}
			return (
				<>
					{
						activity[name] && <img style={{width: '100px', height: '100px'}} src={activity[name]} />
					}
					<span className={styles['upload-logo']}>
						<UploadImg text="选择文件" callback={uploadCallback}/>
					</span>
					<span>
						图片要求：1. 格式为.png 2.底色透明
					</span>
				</>
			)
		}

		function onChangeForm(activity: any) : any{
			console.log(activity, 'activity')
			setActivity(activity)
		}

		function onTableDataChange (data) {
			console.log(data, 'onTableDataChange')
			setAwardList([...data]);
		}

    return (
        <div>
            <FormRender
                propsSchema={isAdd ? SCHEMA1 : SCHEMA2}
                onValidate={setValid}
                formData={activity}
                onChange={activity => onChangeForm(activity)}
                displayType="row"
                showDescIcon={true}
								widgets={{ uploadImg: LogoUpload }}
            />
						<div style={{fontSize: '16px', fontWeight: 'bold', marginTop: '32px', borderBottom: '1px solid #eee'}}>奖励txt文档 每行一个兑换码</div>
						<AwardTable tableData={awardList} onTableDataChange={onTableDataChange} />

            <div className="u-r-middle" style={{margin: 20}}>
                <Button type="primary" onClick={() => {setIsShowAdd(false)}} style={{marginRight: 20}}>取消</Button>
                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要提交么'}
                    onConfirm={postConfig}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button
                        type="danger" 
                    >
                        提交
                    </Button>
                </Popconfirm>
            </div>
        </div>
    )
}