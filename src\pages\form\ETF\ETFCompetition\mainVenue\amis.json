{"type": "page", "title": "ETF大赛主会场", "body": [{"type": "form", "api": {"method": "post", "url": "/common_config/hash_data_save", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "mainVenue", "value": "{\"mainBanners\": ${ENC<PERSON><PERSON><PERSON><PERSON><PERSON>(mainBanners)}, \"mineBanners\": ${ENCODEJSON(mineBanners)}, \"signButtonText\": \"${signButtonText}\", \"cornerMarkText\": \"${cornerMarkText}\"}"}, "messages": {}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "id": "u:e11150b10cb7", "body": [{"type": "combo", "label": "主页banner", "name": "mainBanners", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:67e3b77a6472"}, "items": [{"type": "input-image", "name": "bannerImage", "id": "u:876e105ff33e", "label": "banner图片（718*180）", "accept": ".jpeg, .jpg, .png", "uploadType": "fileReceptor", "proxy": true, "multiple": false, "hideUploadButton": false, "autoUpload": true, "fixedSize": false, "mode": "horizontal", "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}, "limit": {"height": 180, "width": 718}}, {"type": "input-text", "name": "jumpUrl", "id": "u:a25e583682e2", "label": "跳转链接", "mode": "horizontal"}, {"type": "input-number", "label": "权重", "name": "sortWeight", "keyboard": true, "id": "u:eb4b7b8f0753", "step": 1, "mode": "horizontal"}, {"type": "select", "label": "用户范围", "name": "qsId", "id": "u:aa5b21659e67", "multiple": false, "mode": "horizontal", "source": "${brokerList}", "labelField": "brokerName", "valueField": "brokerId"}], "id": "u:fe2d0408c069", "syncFields": [], "multiLine": true, "value": "${ISEMPTY(config.mainBanners)? [] : config.mainBanners}"}, {"type": "combo", "label": "我的比赛banner", "name": "mineBanners", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:5311760718d8"}, "items": [{"type": "input-image", "name": "competitionBannerImage", "id": "u:876e105ff33a", "label": "我的比赛banner（686*172）", "accept": ".jpeg, .jpg, .png", "uploadType": "fileReceptor", "proxy": true, "multiple": false, "hideUploadButton": false, "autoUpload": true, "fixedSize": false, "mode": "horizontal", "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}, "limit": {"height": 172, "width": 686}}, {"type": "input-text", "name": "competitionJumpUrl", "id": "u:a25e583682e4", "label": "跳转链接", "mode": "horizontal"}, {"type": "input-number", "label": "权重", "name": "sortWeight", "keyboard": true, "id": "u:eb4b7b8f0754", "step": 1, "mode": "horizontal"}, {"type": "select", "label": "用户范围", "name": "qsId", "id": "u:aa5b21659e67", "multiple": false, "mode": "horizontal", "source": "${brokerList}", "labelField": "brokerName", "valueField": "brokerId"}], "id": "u:398af88d0253", "syncFields": [], "multiLine": true, "value": "${ISEMPTY(config.mineBanners)? [] : config.mineBanners}"}, {"type": "input-text", "id": "u:a25e583682e1", "label": "报名按钮文案", "name": "signButtonText", "mode": "horizontal", "value": "${config.signButtonText}"}, {"type": "input-text", "label": "报名角标文案", "name": "cornerMarkText", "id": "u:5c261f545f4d", "mode": "horizontal", "value": "${config.cornerMarkText}"}], "title": "主会场运营位", "mode": "horizontal", "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "mainVenue"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}, "submitText": "保存"}], "regions": ["body", "header"], "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "brokerList"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    brokerList: config,\r\n  },\r\n};"}, "id": "u:6dfddf5824d0"}