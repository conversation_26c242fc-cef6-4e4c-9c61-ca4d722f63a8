{"type": "page", "title": "ETF大赛圈子配置", "body": [{"type": "form", "title": "圈子", "body": [{"label": "圈子模块标题", "type": "input-text", "name": "title", "id": "u:ae8ac93dd548", "mode": "horizontal", "required": true, "value": "${title}"}, {"type": "input-text", "label": "简介", "name": "desc", "id": "u:4c72764b7424", "mode": "horizontal", "required": true, "value": "${desc}"}, {"type": "input-text", "label": "跳转链接", "name": "jumpUrl", "id": "u:a983120c5792", "mode": "horizontal", "required": true, "value": "${jumpUrl}"}, {"type": "combo", "label": "弹幕", "name": "commentList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:cd3d7f176276"}, "items": [{"type": "input-text", "name": "comment", "placeholder": "文本", "id": "u:ca5980e48712", "required": true}], "id": "u:1ffa28d1fc50", "syncFields": [], "mode": "horizontal", "required": true, "flat": true, "value": "${commentList}"}], "id": "u:043c6826ba32", "submitText": "保存", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "bulletComment", "value": "{\"title\": \"${title}\", \"desc\": \"${desc}\", \"jumpUrl\": \"${jumpUrl}\", \"commentList\": \"${commentList}\"}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "bulletComment"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: config\r\n};"}}], "regions": ["body", "header"], "id": "u:9e10aa293e97"}