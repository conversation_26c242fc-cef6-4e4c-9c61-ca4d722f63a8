import { useState, useRef, useEffect } from 'react';

const getArr = (num: number) => {
  let arr = [];
  for (let i = 0; i < num; i++) {
    arr[i] = i;
  }
  return arr;
};

export const useFieldArray = ({ arrNum = 1 } = {}) => {
  const arrRef = useRef<{ maxIndex: number }>({ maxIndex: arrNum - 1 });
  const [arr, setArr] = useState(getArr(arrNum));

  const initArr = (arrNum: number) => {
    arrRef.current.maxIndex = arrNum - 1;
    setArr(getArr(arrNum));
    console.log('arrNum changed', arrNum, arr);
  }

  // useEffect(() => {
  //   initArr(arrNum);
  // }, [arrNum]);

  const onAdd = () => {
    arrRef.current.maxIndex++;
    setArr([...arr, arrRef.current.maxIndex]);
  };

  const onRemove = (item: typeof arrRef.current.maxIndex) => {
    // 直接过滤掉要删除的项，保持其他项的索引不变
    const filteredArr = arr.filter(i => i !== item);
    setArr(filteredArr);
  };

  return {
    initArr,
    arr,
    onAdd,
    onRemove,
  };
};
