import React, { useState, useEffect } from 'react';
import { Table, But<PERSON>, Popconfirm, Upload, Input, message } from 'antd';
import api from 'api';

const {
	uploadEarningBonus,
	getUnusedAward,
	getUsedAward
} = api

const CustomTable = ({ tableData, onTableDataChange }) => {
  const [dataSource, setDataSource] = useState<any[]>([]);
	const [file, setFile] = useState<File | null>(null)

  useEffect(() => {
		console.log(tableData, 'tableData');
    setDataSource(tableData || []);
  }, [tableData]);

  const handleDelete = (record, index: number) => {
		dataSource[index] = {clear: true}
		onTableDataChange(dataSource);
  };
  const handleDownloadUnUsed = (record, index: number) => {
		getUnusedAward({coupon: record.coupon}).then((res) => {
			console.log(res);
			downloadTxt('未使用的券',res?.data?.codes)
		})
  };
	const handleDownloadUsed = (record, index: number) => {
		getUsedAward({coupon: record.coupon}).then((res) => {
			const codes = res?.data?.codes;
			if (!codes) {
				message.warn(`没有使用的券`);
				return;
			}
			const list = Object.keys(codes).map(key => `${key} : ${codes[key]}`);
			downloadTxt('使用的券',list);
		})
  };

function downloadTxt(title, val) {
	if (!val) {
		message.warn(`没有${title}`);
		return;
	}
	let str = "";
	val.forEach(item => {
		str += `${item}\r\n`;
	});
	const allStr = title + "\r\n" + "\r\n" + str;
	let export_blob = new Blob([allStr]);
	let save_link = document.createElement("a");
	save_link.href = window.URL.createObjectURL(export_blob);
	save_link.download = title + ".txt";
	document.body.appendChild(save_link);
	save_link.click();
	document.body.removeChild(save_link);
}


	function addFile(_file: File, index: number) {
		if (file) {
				message.error('只能上传一个文件，请先删除原先文件')
				return false
		}
		uploadFile(_file, index)
		console.log('add', index, _file, 'addFile')
		return false
	}
	/**
	 * 上传文件
	 * @param callback 
	 */
	function uploadFile(_file: File, index: number) {
		const item = dataSource[index];
		let _data = new FormData()
		_data.append('file', (_file as File))
		_data.append('coupon', item.coupon || '')
		console.log('upload', index, file, 'index');
		uploadEarningBonus(_data).then((res: any) => {
			console.log(res, 'uploadFile xxx')
				if (res.statusCode === 200) {
						message.success('文件上传成功')
						dataSource[index]['coupon'] = res?.data?.coupon;
						dataSource[index]['clear'] = false;
						onTableDataChange(dataSource)
				}
		}).catch(e => {
				message.error('文件上传失败')
		})
	}

	function changeFormData(value, index, key) {
		console.log(value, index, key, dataSource, 'changeFormData');
		if (key === 'weight') {
			const isNumeric = !isNaN(parseFloat(value)) && isFinite(value);
			if (!isNumeric) {
				message.error('请输入数字');
				return;
			}
		}
		dataSource[index][key] = value.trim();
		onTableDataChange(dataSource)
	}


	const renderUpload = (text, record, index) => {
		console.log(text, record, index, 'renderUpload');
		return (<Upload
				showUploadList={!record?.clear}
				beforeUpload={(file) => addFile(file, index)}
			>
					<Button type="primary">上传文件(一行一个兑换码的txt文件)</Button>
			</Upload>)
	}
  /** 操作 */
  const renderOperate = (text: string, record: any, index: number) => {
    return (
      <div className="u-l-middle">
        <Popconfirm
          title="是否确认清除？"
          okText="确认"
          cancelText="取消"
          onConfirm={() => {
            handleDelete(record, index);
          }}
        >
          <Button type="link" className="g-ml10">
            <span style={{ color: '#ff0000' }}>清除内容</span>
          </Button>
        </Popconfirm>
        <Button
          type="link"
          onClick={() => {
            handleDownloadUnUsed(record, index);
          }}
        >
          下载未使用的码
        </Button>
				<Button
          type="link"
          onClick={() => {
            handleDownloadUsed(record, index);
          }}
        >
         	下载使用的码
        </Button>
      </div>
    );
  };
  const renderType = (text, record) => (record.type === 1 ? '社群' : '直播');
	const renderInput = (text, record, index, key) => {
		console.log(text, record, index, key, 'render input')
		return <Input value={text} onChange={(e) => changeFormData(e.target.value, index, key)}/>
	}
  const columns = [
    { title: '奖励名称', dataIndex: 'name',width: '120px', key: 'name', render: (text, record, index) => renderInput(text, record, index, 'name') },
    { title: '比例', dataIndex: 'weight',width: '80px', key: 'weight', render: (text, record, index) => renderInput(text, record, index, 'weight') },
    { title: 'txt文件上传', key: 'file', width: '120px', render: renderUpload },
    { title: '奖励领取链接', width: '140px', dataIndex: 'url', key: 'url', render: (text, record, index) => renderInput(text, record, index, 'url') },
    { title: 'txt码', key: 'coupon',dataIndex: 'coupon', width: '100px' },
    { title: '操作', key: '_operate', width: '200px', render: renderOperate },
  ];

  return (
    <>
      <Table
        rowKey="index"
        bordered
        style={{ width: '70%' }}
        // rowSelection={rowSelection}
        columns={columns}
        dataSource={dataSource}
        pagination={false}
      />
      {/* <Button type="dashed" onClick={addColumn} style={{ width: '30%' }}>
        <span style={{ color: '#1890ff' }}>新增</span>
      </Button> */}
    </>
  );
};

export default CustomTable;
