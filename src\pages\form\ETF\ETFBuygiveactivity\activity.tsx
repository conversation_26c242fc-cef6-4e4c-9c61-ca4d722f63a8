import React, { useEffect, useState } from 'react';
import FormRender from 'form-render/lib/antd';
import { Button, Popconfirm, message, Input } from 'antd';
import UploadImg from '../../components/uploadFile';
import AwardTable from './awardTable/index';
import getSchema from './config';
import api from 'api';
import styles from './index.less';
const SCHEMA1 = getSchema(false);
const SCHEMA2 = getSchema(true);

interface ETFActivityItem {
  activityId: string;
  type: string;
  cardType: string;
  activityName: string;
  startDate: string;
  endDate: string;
  status: string;
  title: string;
  subTitle: string;
  tipTitle: string;
  tipContext: string;
  recommendTitle: string;
  recommendCode: string;
  recommendName: string;
  profitDescribe: string;
  productTitle: string;
  purchaseContext: string;
  windowText: string;
  activityRules: string | any;
  codeRate: string;
  cashVoucherList: any;
}


const { fetchETFActivity, uploadETFActivityFile, fetchETFActivityAward, postEtfBuyGiveActivity } =
  api;

export default function ({
  isAdd = false,
  isShowAdd,
  activities = [],
  activity,
  setActivity,
  awardList,
  setAwardList,
  setIsShowAdd = () => {},
  handleFetchETFActivity = () => {},
}: {
  isAdd?: boolean;
  isShowAdd: boolean;
  activities: any;
  activity?: any;
  setActivity: any;
  awardList: any;
  setAwardList: Function;
  setIsShowAdd: Function;
  handleFetchETFActivity: Function;
}) {
  const [valid, setValid] = useState([]);
  const [file, setFile] = useState<File | null>(null);

  useEffect(() => {
    if (!isShowAdd) {
      setFile(null);
    }
  }, [isShowAdd]);

  /**
   * 提交
   */
  function postConfig() {
    console.log(activity, activities, valid, 'activity');
    if (activities.filter((item) => item.activeId === activity.activeId).length > (isAdd ? 0 : 1))
      return message.error('activeId重复，需为唯一');
    if (valid.length > 0) return message.error('必填项未填');

    if (activity.activityRules.length === 0) return message.error('请填写规则');
    const item = awardList.filter((item) => item.coupon && item.url);
    if (item && item.length > 0) {
      message.error('奖品码和领取奖励不能同时存在');
      return;
    }
    let _form: any = { ...activity };
    _form.award = awardList;
    postEtfBuyGiveActivity({
      key: 'etf-buygiveactivity',
      propName: _form.activeId,
      value: JSON.stringify(_form),
    })
      .then((res: any) => {
        console.log(res, 'res');
        _.hideFundLoading();
        if (res.code === '0000') {
          setIsShowAdd(false);
          message.success(isAdd ? '添加完成' : '修改完成');
          handleFetchETFActivity();
        }
      })
      .catch(() => {
        _.hideFundLoading();
      });
  }

  function onChangeForm(activity: any): any {
    console.log(activity, 'activity');
    setActivity(activity);
  }

  function onTableDataChange(data, itemIndex) {
    console.log(data, 'onTableDataChange');
    awardList[itemIndex].data = data;
    setAwardList([...awardList]);
  }

  const handleDelete = (index: number) => {
    awardList.splice(index, 1);
    setAwardList([...awardList]);
  };

  const handleClearAll = (index: number) => {
    if (awardList[index] && awardList[index].data) {
      awardList[index].data = awardList[index].data.map((record) => {
        return {
          clear: true,
          name: record.name || '',
          amount: record.amount || '',
        };
      });
      setAwardList([...awardList]);
    }
  };

  const handleAdd = () => {
    awardList.push({
      company: '',
      data: [
        {
          name: '2元',
          coupon: '',
          clear: false,
          url: '',
          amount: '2'
        },
        {
          name: '20元',
          url: '',
          coupon: '',
          clear: false,
          amount: '20'
        },
      ],
    });
    setAwardList([...awardList]);
  }

  const renderList = () => {
    const renderElements = [];
    awardList.forEach((item, index) => {
      renderElements.push(
        <div
          style={{
            border: '1px solid #eee',
            marginBottom: '35px',
          }}
        >
          基金公司：
          <Input
            value={item.company}
            style={{
              width: '250px',
              marginRight: '15px',
            }}
            onChange={(e) => {
              item.company = e.target.value;
              setAwardList([...awardList]);
            }}
          />
          基金公司code：
          <Input
            value={item.companyCode}
            style={{
              width: '250px',
            }}
            onChange={(e) => {
              item.companyCode = e.target.value;
              setAwardList([...awardList]);
            }}
          />
          <AwardTable
            itemIndex={index}
            tableData={item.data}
            onTableDataChange={onTableDataChange}
          />
          <div className="u-l-middle g-mt10">
            <Button type="danger" className="g-ml10" onClick={() => handleDelete(index)}>
              删除
            </Button>
            <Button type="danger" className="g-ml10" onClick={() => handleClearAll(index)}>
              全部清除
            </Button>
          </div>
        </div>,
      );
    });
    return renderElements;
  };

  return (
    <div>
      <FormRender
        propsSchema={isAdd ? SCHEMA1 : SCHEMA2}
        onValidate={setValid}
        formData={activity}
        onChange={(activity) => onChangeForm(activity)}
        displayType="row"
        showDescIcon={true}
      />
      <div
        style={{
          fontSize: '16px',
          fontWeight: 'bold',
          marginTop: '32px',
          borderBottom: '1px solid #eee',
        }}
      >
        奖励txt文档 每行一个兑换码
      </div>
      {renderList()}
      <div >
        <Button onClick={() => {
          handleAdd();
        }}>新增</Button>
      </div>

      <div className="u-r-middle" style={{ margin: 20 }}>
        <Button
          type="primary"
          onClick={() => {
            setIsShowAdd(false);
          }}
          style={{ marginRight: 20 }}
        >
          取消
        </Button>
        <Popconfirm
          placement="rightBottom"
          title={'你确定要提交么'}
          onConfirm={postConfig}
          okText="确认"
          cancelText="取消"
        >
          <Button type="danger">提交</Button>
        </Popconfirm>
      </div>
    </div>
  );
}
