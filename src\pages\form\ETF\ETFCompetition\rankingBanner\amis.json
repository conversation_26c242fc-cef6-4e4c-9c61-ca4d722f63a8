{"type": "page", "title": "ETF大赛排行榜", "body": [{"type": "form", "title": "排行榜banner运营位", "body": [{"label": "涨幅榜跳转链接", "type": "input-text", "name": "risingJumpUrl", "id": "u:dc0094926555", "value": "${config.risingJumpUrl}"}, {"type": "input-image", "label": "banner图（686*172）", "name": "risingImage", "id": "u:b002b107d51e", "accept": ".jpeg, .jpg, .png", "uploadType": "fileReceptor", "proxy": true, "multiple": false, "hideUploadButton": false, "autoUpload": true, "fixedSize": false, "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}, "limit": {"height": 172, "width": 686}, "value": "${config.risingImage}"}, {"type": "input-text", "label": "周榜跳转链接", "name": "weekJumpUrl", "id": "u:b0c2bf926e15", "value": "${config.weekJumpUrl}"}, {"type": "input-image", "label": "banner图（686*172）", "name": "weekImage", "id": "u:7279f364785f", "accept": ".jpeg, .jpg, .png", "uploadType": "fileReceptor", "proxy": true, "multiple": false, "hideUploadButton": false, "autoUpload": true, "fixedSize": false, "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}, "limit": {"height": 172, "width": 686}, "value": "${config.weekImage}"}, {"type": "input-text", "label": "月榜跳转链接", "name": "monthJumpUrl", "id": "u:bac1c39c4acf", "value": "${config.monthJumpUrl}"}, {"type": "input-image", "label": "banner图（686*172）", "name": "monthImage", "id": "u:f2f7a9d47dd8", "accept": ".jpeg, .jpg, .png", "uploadType": "fileReceptor", "proxy": true, "multiple": false, "hideUploadButton": false, "autoUpload": true, "fixedSize": false, "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}, "limit": {"height": 172, "width": 686}, "value": "${config.monthImage}"}, {"type": "input-text", "label": "总榜跳转链接", "name": "totalJumpUrl", "id": "u:35ca734ca5ef", "value": "${config.totalJumpUrl}"}, {"type": "input-image", "label": "banner图（686*172）", "name": "totalImage", "id": "u:10eccb7523f4", "accept": ".jpeg, .jpg, .png", "uploadType": "fileReceptor", "proxy": true, "multiple": false, "hideUploadButton": false, "autoUpload": true, "fixedSize": false, "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}, "limit": {"height": 172, "width": 686}, "value": "${config.totalImage}"}], "mode": "horizontal", "id": "u:1903eac428fc", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "rankingBanner", "value": "{\"risingJumpUrl\": \"${risingJumpUrl}\", \"risingImage\": \"${risingImage}\", \"weekJumpUrl\": \"${weekJumpUrl}\", \"weekImage\": \"${weekImage}\", \"monthJumpUrl\": \"${monthJumpUrl}\", \"monthImage\": \"${monthImage}\", \"totalJumpUrl\": \"${totalJumpUrl}\", \"totalImage\": \"${totalImage}\"}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "rankingBanner"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}, "submitText": "保存"}], "regions": ["body", "header"], "id": "u:ceeab08c548d"}