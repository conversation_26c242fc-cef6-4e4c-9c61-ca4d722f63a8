import React, { useEffect, useState } from 'react';
import classnames from 'classnames';
import api, {gen} from 'api';
import Activity from './activity';
import { Button, message, Drawer, Row, Col, Radio, Popconfirm} from 'antd';
import { TITLES } from './config';
import styles from './index.less';
import { DraggableArea } from '@/components/DragTags';

const { getAnswer } = api;


export default function(props: any) {
  const [type, setType] = useState('');
  const [isShowAdd, setIsShowAdd] = useState(false); //是否显示modal
  const [selectedActivity, setSelectedActivity] = useState({}); //选中的活动
  const [queryDone, setQueryDone] = useState(false); //选中的活动
  const [modify, setModifyStatus] = useState('0'); // 0代表新增 1 代表查看 2 代表修改 3代表审核
  const [tabId, setTabId] = useState('');
  const [articles, setArticles] = useState([]);
  const [article, setArticle] = useState({});

  useEffect(() => {
    handleFetchETFActivity();
  }, []);

  /**
   * 查询活动列表
   */

  function handleFetchETFActivity() {
    _.fundLoading();
    getAnswer().then(data => {
      const info = JSON.parse(data.data) || {};
      console.log(info, 'getAnswer me');
      info && setSelectedActivity(info);
      setQueryDone(true);
      const tabs = info?.tabs || [];
      const tabId = tabs[0]?.id || '';
      setTabId(tabId);
      if (tabId) {
        getArticles(tabId)
      } else {
        setArticles([]);
      }
      console.log(tabs[0],'tabs[0]')
      _.hideFundLoading();
    }).catch(() => {
      _.hideFundLoading();
    })
  }

  function onChangeTab(e) {
    console.log(e.target.value, e, 'onChangeTab');
    setTabId(e.target.value);
    getArticles(e.target.value);
  }

  /**
   * 查询单个
   * @param activityId
   */
  function modifyCard(type, modify) {
    showAdd(type, modify);
  }

  function showAdd(typeEdit, modify) {
    setType(typeEdit);
    setModifyStatus(modify);
    setIsShowAdd(true);
    if (typeEdit === 'article' && !modify) {
      console.log('setArticle');
      setArticle({});
    }
  }

  const handleDelArticle = (index: number) => {
    let _funds: FundItem[] = JSON.parse(JSON.stringify(articles));
    _funds.splice(index, 1);
    // _funds.forEach((item, index) => {
    //     item.id = index.toString();
    // })
    // setFunds(_funds);
    setArticles(_funds);
}

  function getArticles(tabId) {
    const getArticle = gen(`get /common_config/kv_data_get?key=${tabId}`);
    getArticle().then(res => {
      if (!res || !res.data) {
        setArticles([]);
        return;
      }
      const list = JSON.parse(res.data);
      setArticles(list);
    });
  }

  function saveArticle(newArticle) {
    const articlesFormat = [...articles];
    if (modify === '1') {
      const index = articles.findIndex(item => item.id === newArticle.id);
      articlesFormat.splice(index, 1, newArticle);
    } else {
      articlesFormat.push(newArticle);
    }
    saveConfig(articlesFormat);
  }

  function saveConfig(articlesFormat) {
    const postArticle = gen(`post /common_config/kv_data_save?key=${tabId}`);
    postArticle({
      value: articlesFormat ? JSON.stringify(articlesFormat) : JSON.stringify(articles),
    }).then(res => {
      console.log(res, 'res')
      if (res.code === '0000') {
        message.success('保存成功');
        getArticles(tabId);
      } else {
        message.error('保存失败，请重新再试！');
      }
    }).catch(() => {
      message.error('保存失败，请重新再试！');
    });
  }

  return (
    <div className={styles["etf-answer"]}>
      <div style={{ marginTop: '40px',  marginBottom: '40px' }}>
        banner、卡片、提问配置：<Button type="primary" style={{ marginLeft: '20px' }} onClick={() => modifyCard('banner')}>编辑</Button>
      </div>
      <div style={{ marginTop: '40px',  marginBottom: '20px' }}>
        <Radio.Group value={tabId} buttonStyle="solid" style={{ marginRight: '20px' }} onChange={onChangeTab}>
          {selectedActivity.tabs && selectedActivity.tabs.map(tab => <Radio.Button value={tab.id} key={tab.id}>{tab.name}</Radio.Button>)}
        </Radio.Group>
        
        <Button type="primary" onClick={() => modifyCard('tab')}>{tabId ? 'tab编辑' : 'tab创建'}</Button>
      </div>
      <div >
        {/* <h2>文章列表</h2> */}
        <Row gutter={[32, 16]} style={{marginBottom: '24px'}}>
          <Col span={2}>
            <span style={{fontWeight: 'bold'}}>排序</span>
          </Col>
          <Col span={2}>
            <span style={{fontWeight: 'bold'}}>ID</span>
          </Col>        
          <Col span={5}>
            <div className="m-value-item" style={{fontWeight: 'bold'}}>
              名称
            </div>
          </Col>
          <Col span={5}>
            <div style={{fontWeight: 'bold'}}>
              跳转链接
            </div>
          </Col>
          <Col span={3}>
            <div style={{fontWeight: 'bold'}}>
              操作
            </div>
          </Col>
        </Row>

        {articles.length ? <div className={classnames(styles['m-list'])}>
          <DraggableArea
            isList
            style={{width: '100%'}}
            tags={ articles}
            render={({tag, index}: {tag: FundItem, index: number}) => (
              // <div className={classnames(styles['tag'], styles['m-fund-item'])}>
                <Row style={{height: "56px", overflow: 'hidden'}} gutter={[32, 16]}>
                  <Col span={2}>
                    <span >{index + 1}</span>
                  </Col>
                  <Col span={2}>
                    <span>{tag.id}</span>
                  </Col>        
                  <Col span={5} style={{height: "56px", overflow: 'hidden'}}>
                    <div className="m-value-item" >
                      {tag.title}
                    </div>
                  </Col>
                  <Col span={5}>
                    <div >
                      {tag.type === 'url' ? '链接' : '文本'}
                    </div>
                  </Col>
                  <Col span={3}>
                    <Button type="primary" style={{marginRight: '6px'}} onClick={() => {
                      setArticle(tag);
                      showAdd('article', '1');
                    }}>编辑</Button>
                    <Popconfirm title="确认删除吗？" onConfirm={() => handleDelArticle(index)} okText="确认" cancelText="取消">
                        <Button type="danger">删除</Button>
                    </Popconfirm>
                  </Col>
                  <Col span={2}>
                      <img className={classnames(styles['m-logo'])} src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAMAAACahl6sAAAAgVBMVEUAAAAUEBwUEBwUEBwMCxYTEBsTDxsTDxsTDxwTDxsTDxsTDxsTDxsTDxsTDhoRCxsTDxwTDxsTDBkTDxwTDxsTDxsSDxoRDRsQDxoVABUUDxsTDxsTDxsRDhoNDRsTDxsTDxoTEBkTDxoAABgSDxsSDhkSDhsSCRoAAAAUDxoUEBximHrhAAAAKnRSTlMAQIDAF/Vm1/nFvaighWstpYwn7su6YksxDOqwX1cS4YhRTgqWRjcdCHVGYskOAAAB5klEQVR42u3dWW7bMBSF4Wtr8CRLthzPTuK0SZve/S+wDYqgaNGH4PJBx8z/7eBANC2KR5QBAAAAAAAAAAAgV8f79WJeDGi+WN9/tVTdyiWsOksiEuNNY3HHwoXM4+Nr7lK2FrR2MVcL+eZyDhZx53JmmVyQ2CVZuqBxIMjWBUUmrokLmgSCuKIqEGTjgjafeWgtXNAqEGTngi6BIKULerA8xlZjEWXlYjYPFvLkYvb25vZvt8YWVk9dxrS2BAeZRcmsszSXmQsodpauHA2uNAAAAAAAAAAA/qfc1+NB1fvS0rUS2wqz2tLsTi7idLFfsthEHFvY1aUsLeiLi3mykO9yhYGqvPU6+bvGPnOppnVB9e0X43/b5tJpPOXSMp0GgshNvtG67KMLirRMzy7onMv02waCdC6otwCJFdXfCos4uJzeQn64mDaT196utExpmb4Ta5nm8YAOAAAAAAAAAJClYz8aVH80S9cvBc6nmix7S9PJ1AbOnVkmW2+7TNqytExpmf6LliktU1qmHzBgy1Tov/CPKpe6bDYt08c8jsR2vwsEeXZBz4EgLy5oZHkc+DvJ4+bX/cVCGhfTWJDUxy7cC4t6lTryd/Fqca3M4qpqLUkp8kNpSj42BAAAAAAAAAAAAGk/ASbTtk4dLR//AAAAAElFTkSuQmCC" />
                  </Col>
                </Row>
              // </div>
            )}
            onChange={(data:any) => {
              setArticles(data)
            }}
          >
          </DraggableArea>
        </div>
        : <Row gutter={[32, 16]}><Col span={19}><div style={{width: '100%', textAlign: 'center'}}>此tab暂无文章</div></Col></Row>}
      </div>
      {tabId && <div style={{width: '100%', textAlign: 'left', display: 'flex', justifyContent: 'flex-start'}}>
        <Button type="primary" style={{ marginTop: '20px' }} onClick={() => showAdd('article')}>
          新增文章
        </Button>
        <Popconfirm
          placement="rightBottom"
          title={'你确定要保存么'}
          onConfirm={() => saveConfig()}
          okText="确认"
          cancelText="取消"
        >
          <Button type="primary" style={{ marginTop: '20px', marginLeft: '20px' }}>
            保存
          </Button>
        </Popconfirm>
      </div>}


      <Drawer
        title={TITLES[type]?.drawerTitle}
        width={1200}
        onClose={() => {
          setIsShowAdd(false);
        }}
        visible={isShowAdd}
        bodyStyle={{ paddingBottom: 80 }}
      >
        {isShowAdd && <Activity
          type={type}
          tabId={tabId}
          modify={modify}
          isShowAdd={isShowAdd}
          activity={selectedActivity}
          article={article}
          setArticle={setArticle}
          setActivity={setSelectedActivity}
          setIsShowAdd={setIsShowAdd}
          handleFetchETFActivity={handleFetchETFActivity}
          saveArticle={saveArticle}
        />}
      </Drawer>
    </div>
  );
}
