import React, { useEffect } from 'react';
import 'moment/locale/zh-cn';
import amisJSON from './amis.json';
import amisEnv from 'functions/amisEnv';
import { formatUrlParams} from '../../../abtest/util';
import { defaultActivityId } from '../index';

export default function() {
    let amisScoped:any;
    const init = () => {
        let amis = amisRequire('amis/embed');
        amisScoped = amis.embed('#etfNoticeAndPopup', {
          ...amisJSON,
          data:{
            activityId: formatUrlParams()?.activityId ? formatUrlParams().activityId: defaultActivityId
          }}, {}, amisEnv());
    }
    useEffect(() => {
      init();
      return () => {
        amisScoped.unmount();
      }
    }, [])
    
    return (
      <div id='etfNoticeAndPopup'></div>
    )
}

