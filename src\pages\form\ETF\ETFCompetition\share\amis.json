{"type": "page", "title": "ETF大赛分享回流", "body": [{"type": "form", "title": "分享回流", "body": [{"type": "combo", "label": "", "name": "shareList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:202695c17226"}, "items": [{"type": "input-text", "name": "interval", "placeholder": "", "id": "u:97a3a7dd2678", "label": "收益率区间", "required": true}, {"type": "input-text", "label": "对应文案", "name": "content", "id": "u:6268ab75c951", "required": true}], "id": "u:8806b858a079", "strictMode": true, "syncFields": [], "value": "${config}"}], "id": "u:958607b9caf8", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "share", "value": "${ENCODEJSON(shareList)}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "share"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}, "submitText": "保存"}], "regions": ["body", "header"], "id": "u:c866d240dd25"}