export default function (isEdit) {
  return {
    type: 'object',
    required: [
      'activeId',
      'activeName',
      'status',
      'sectorETF',
      'broadMarketETF',
      'instantSettlementETF',
      'beginTime',
      'endTime',
      'activityRules',
    ],
    properties: {
      activeId: {
        title: '活动id',
        type: 'string',
        pattern: /^\w+$/,
        description: '需要用英文输入，唯一标识符，不能重复',
        'ui:options': {},
        'ui:disabled': isEdit ? true : false,
      },
      activeName: {
        title: '活动名称',
        type: 'string',
        'ui:options': {},
      },
      status: {
        title: '活动状态（紧急状态下线使用）',
        type: 'string',
        enum: ['1', '0'],
        enumNames: ['开始', '结束'],
        'ui:widget': 'radio',
      },
      sectorETF: {
        title: '行业ETF',
        type: 'array',
        items: {
          type: 'object',
          properties: {
            etfName: {
              title: '产品名称',
              type: 'string',
              'ui:options': {},
            },
            etfCode: {
              title: 'etf产品code',
              type: 'string',
              pattern: /^[a-zA-Z0-9]+$/,
              description: '仅允许输入字母或数字',
              'ui:options': {},
            },
            etfMarket: {
              title: 'etf市场code',
              type: 'string',
              pattern: /^[a-zA-Z0-9]+$/,
              description: '仅允许输入字母或数字',
              'ui:options': {},
            },
            companyCode: {
              title: '基金公司code',
              type: 'string',
              'ui:options': {},
            },
          },
        },
        'ui:options': {},
      },
      broadMarketETF: {
        title: '宽基ETF',
        type: 'array',
        items: {
          type: 'object',
          properties: {
            etfName: {
              title: '产品名称',
              type: 'string',
              'ui:options': {},
            },
            etfCode: {
              title: 'etf产品code',
              type: 'string',
              pattern: /^[a-zA-Z0-9]+$/,
              description: '仅允许输入字母或数字',
              'ui:options': {},
            },
            etfMarket: {
              title: 'etf市场code',
              type: 'string',
              pattern: /^[a-zA-Z0-9]+$/,
              description: '仅允许输入字母或数字',
              'ui:options': {},
            },
            companyCode: {
              title: '基金公司code',
              type: 'string',
              'ui:options': {},
            },
          },
        },
        'ui:options': {},
      },
      instantSettlementETF: {
        title: 'T+0ETF',
        type: 'array',
        items: {
          type: 'object',
          properties: {
            etfName: {
              title: '产品名称',
              type: 'string',
              'ui:options': {},
            },
            etfCode: {
              title: 'etf产品code',
              type: 'string',
              pattern: /^[a-zA-Z0-9]+$/,
              description: '仅允许输入字母或数字',
              'ui:options': {},
            },
            etfMarket: {
              title: 'etf市场code',
              type: 'string',
              pattern: /^[a-zA-Z0-9]+$/,
              description: '仅允许输入字母或数字',
              'ui:options': {},
            },
            companyCode: {
              title: '基金公司code',
              type: 'string',
              'ui:options': {},
            },
          },
        },
        'ui:options': {},
      },
      beginTime: {
        'ui:labelWidth': 150,
        title: '活动开始时间',
        type: 'string',
        format: 'date',
        'ui:width': '46%',
      },
      endTime: {
        'ui:labelWidth': 150,
        title: '活动结束时间',
        type: 'string',
        format: 'date',
        'ui:width': '46%',
      },
      activityRules: {
        title: '活动规则',
        type: 'array',
        items: {
          type: 'object',
          properties: {
            text: {
              title: '',
              type: 'string',
              'ui:options': {},
            },
          },
        },
        'ui:options': {},
      },
    },
  };
}
