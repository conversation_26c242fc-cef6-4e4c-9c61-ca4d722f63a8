{"type": "page", "title": "ETF大赛公告弹窗", "body": [{"type": "form", "title": "顶部公告配置", "body": [{"label": "公告滚动文案", "type": "input-text", "name": "noticeContent", "id": "u:e7111d7b71d3", "mode": "horizontal", "value": "${config.noticeContent}"}, {"type": "input-text", "label": "公告跳转链接", "name": "noticeUrl", "id": "u:0974be856a23", "mode": "horizontal", "validations": {}, "validationErrors": {}, "showCounter": false, "value": "${config.noticeUrl}"}], "id": "u:b93c32851bb2", "submitText": "保存", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "notice", "value": "{\"noticeContent\": \"${noticeContent}\", \"noticeUrl\": \"${noticeUrl}\"}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "notice"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}}, {"type": "form", "title": "弹窗配置", "body": [{"type": "combo", "label": "", "name": "popupList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:b615a96d7747"}, "items": [{"type": "input-text", "name": "name", "id": "u:582896be78a9", "label": "名称", "mode": "horizontal", "required": true, "unique": true}, {"type": "input-datetime-range", "name": "validTime", "placeholder": "请选择日期时间范围", "id": "u:21addd91c1ad", "label": "日期范围", "mode": "horizontal", "inputFormat": "YYYY-MM-DD HH:mm:ss", "format": "X", "minDate": "", "maxDate": "", "value": "", "ranges": [], "hidden": false, "required": true}, {"type": "input-image", "label": "图片上传（560*740）", "name": "imageUrl", "autoUpload": true, "proxy": true, "uploadType": "fileReceptor", "imageClassName": "r w-full", "id": "u:6b0d8e94e484", "accept": ".jpeg, .jpg, .png", "multiple": false, "hideUploadButton": false, "fixedSize": false, "mode": "horizontal", "required": true, "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}, "limit": {"height": 740, "width": 560}}, {"type": "input-text", "label": "跳转链接", "name": "jumpUrl", "id": "u:136669ea1f27", "mode": "horizontal"}, {"type": "select", "label": "人群类型", "name": "userType", "options": [{"label": "全部用户", "value": 0}, {"label": "已报名", "value": 1}, {"label": "完成报名未绑定", "value": 2}, {"label": "完成绑定未交易", "value": 3}, {"label": "完成报名有自选", "value": 4}, {"label": "有现金奖励", "value": 5}], "id": "u:68934c578318", "multiple": false, "mode": "horizontal", "required": true, "value": 0}], "id": "u:0d1fbf05d5f8", "strictMode": true, "syncFields": [], "multiLine": true, "value": "${config}"}], "id": "u:3ec481ba144d", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "popup", "value": "${ENCODEJSON(popupList)}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "popup"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}, "submitText": "保存"}], "regions": ["body", "header"], "id": "u:dc588a95fe28"}