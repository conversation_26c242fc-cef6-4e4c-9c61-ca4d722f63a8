{"type": "page", "title": "ETF大赛黑名单配置", "body": [{"type": "form", "title": "黑名单", "body": [{"type": "input-file", "label": "领奖黑名单", "name": "awardBlackList", "id": "u:b3baf0d1fb93", "accept": ".xlsx,.xls,.csv", "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}, "value": "${config.awardBlackList}"}, {"type": "input-text", "label": "报错文案", "name": "award<PERSON><PERSON><PERSON><PERSON><PERSON>nt", "id": "u:22f4f2745df5", "value": "${config.awardBlackContent}"}, {"type": "input-file", "label": "参赛黑名单", "name": "competitionBlackList", "id": "u:66be594decdf", "accept": ".xlsx,.xls,.csv", "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}, "value": "${config.competitionBlackList}"}, {"type": "input-text", "label": "报错文案", "name": "competitionBlackContent", "id": "u:7e11f6d1d5ad", "value": "${config.competitionBlackContent}"}], "id": "u:6e987d87d1d5", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "blackList", "value": "{\"awardBlackList\": \"${awardBlackList}\", \"awardBlackContent\": \"${awardBlackContent}\", \"competitionBlackList\": \"${competitionBlackList}\", \"competitionBlackContent\": \"${competitionBlackContent}\"}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "blackList"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}, "submitText": "保存"}], "regions": ["body", "header"], "id": "u:682519ea108c"}