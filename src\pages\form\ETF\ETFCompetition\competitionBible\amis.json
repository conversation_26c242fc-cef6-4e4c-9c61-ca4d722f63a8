{"type": "page", "title": "ETF大赛赛事宝典", "body": [{"type": "form", "title": "赛事宝典", "body": [{"type": "combo", "label": "顶部运营位", "name": "bannerList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:43a8da1b3b9c"}, "items": [{"type": "input-text", "name": "jumpUrl", "placeholder": "", "id": "u:9b0a7f24d6ed", "label": "跳转链接"}, {"type": "input-image", "label": "banner图", "name": "bannerImage", "autoUpload": true, "proxy": true, "uploadType": "fileReceptor", "imageClassName": "r w-full", "id": "u:de920f032ac8", "accept": ".jpeg, .jpg, .png", "multiple": false, "hideUploadButton": false, "fixedSize": false, "required": true, "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}}], "id": "u:812d51c8aedf", "strictMode": true, "syncFields": [], "maxLength": 3, "value": "${config.bannerList}"}, {"type": "combo", "label": "投教列表", "name": "classList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:2be329728b17"}, "items": [{"type": "input-text", "name": "jumpUrl", "placeholder": "", "id": "u:e3db76abba0e", "label": "跳转链接"}, {"type": "input-image", "label": "切图", "name": "image", "autoUpload": true, "proxy": true, "uploadType": "fileReceptor", "imageClassName": "r w-full", "id": "u:70a9f474d515", "accept": ".jpeg, .jpg, .png", "multiple": false, "hideUploadButton": false, "fixedSize": false, "required": true, "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}}], "id": "u:afc0d6921755", "strictMode": true, "syncFields": [], "maxLength": 3, "minLength": 2, "required": true, "value": "${config.classList}"}, {"type": "combo", "label": "腰部卡片", "name": "cardList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:e03d0ce8edd0"}, "items": [{"type": "input-text", "name": "jumpUrl", "placeholder": "", "id": "u:8f3250b4bc4b", "label": "跳转链接"}, {"type": "input-image", "label": "切图", "name": "image", "autoUpload": true, "proxy": true, "uploadType": "fileReceptor", "imageClassName": "r w-full", "id": "u:82a6732bebcb", "accept": ".jpeg, .jpg, .png", "multiple": false, "hideUploadButton": false, "fixedSize": false, "required": true, "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}}], "id": "u:e06e47fccd32", "strictMode": true, "syncFields": [], "maxLength": 2, "minLength": 2, "required": true, "value": "${config.cardList}"}, {"type": "input-text", "label": "更多跳转链接", "name": "moreJumpUrl", "id": "u:3df7d4d381c1", "required": true, "value": "${config.moreJumpUrl}"}], "mode": "horizontal", "id": "u:46de8f4a73d8", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "competitionBible", "value": "{\"bannerList\": ${<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(bannerList)}, \"classList\": ${ENCODEJSON(classList)}, \"cardList\": ${ENCODEJSON(cardList)}, \"moreJumpUrl\": \"${moreJumpUrl}\"}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "competitionBible"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}, "submitText": "保存"}], "regions": ["body", "header"], "id": "u:2f522bd4d02c"}