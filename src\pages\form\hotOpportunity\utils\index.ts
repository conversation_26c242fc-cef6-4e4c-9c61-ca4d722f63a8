import { env } from 'config';
import moment from 'moment';
import { SelectedETF, OpportunityItem, ETFCustomConfig } from '../types';

export const getOperator = () => JSON.parse(localStorage.getItem('name') as string) || '';

export const getApiPrefix = (type: 'fund') => {
  if (type === 'fund') {
    return env === 'prod' ? 'https://fund.10jqka.com.cn' : 'https://testfund.10jqka.com.cn';
  }
}

/**
 * 获取昨天的日期
 * @returns {string} 昨天的日期，格式为 yyyymmdd
 */
export const getYesterday = (): string => {
  return moment().subtract(1, 'day').format('YYYYMMDD');
}

// 计算概率的工具函数
export const calculateProbability = (selectedCodes: string[], allProductList: SelectedETF[], currentCode: string): string => {
  try {
    if (!selectedCodes || selectedCodes.length === 0 || !allProductList || allProductList.length === 0) {
      return '0.00%';
    }
    
    // 只考虑已选择的产品
    const selectedProducts = allProductList.filter(item => selectedCodes.includes(item.code));
    
    if (selectedProducts.length === 0) {
      return '0.00%';
    }
    
    // 计算已选择产品result的总和
    const totalResult = selectedProducts.reduce((sum, item) => {
      const result = parseFloat(item.result) || 0;
      return sum + result;
    }, 0);
    
    // 如果总和为0，每一项都为0
    if (totalResult === 0) {
      return '0.00%';
    }
    
    // 找到当前产品并计算其概率
    const currentProduct = selectedProducts.find(item => item.code === currentCode);
    if (!currentProduct) {
      return '0.00%';
    }
    
    const currentResult = parseFloat(currentProduct.result) || 0;
    const probability = (currentResult / totalResult) * 100;
    
    return probability.toFixed(2) + '%';
  } catch (error) {
    console.error('计算概率时出错:', error);
    return '0.00%';
  }
};

/**
 * 将表单数据中的selectedEtf字段从字符串数组转换为SelectedETF对象数组
 * @param formData 表单数据
 * @param trackProductMap 赛道产品映射
 * @returns 转换后的表单数据
 */
export const convertSelectedETFFields = (
  formData: Partial<OpportunityItem>,
  trackProductMap: Record<string, SelectedETF[]>
): Partial<OpportunityItem> => {
  // 参数验证
  if (!formData || !trackProductMap) {
    throw new Error('参数不能为空');
  }
  
  // 深拷贝避免修改原数据
  const convertedData = JSON.parse(JSON.stringify(formData));
  
  // 转换场内配置
  if (convertedData.data?.fundConfig?.inside) {
    convertedData.data.fundConfig.inside = convertETFConfigArray(
      convertedData.data.fundConfig.inside,
      trackProductMap
    );
  }
  
  // 转换场外配置
  if (convertedData.data?.fundConfig?.outside) {
    convertedData.data.fundConfig.outside = convertETFConfigArray(
      convertedData.data.fundConfig.outside,
      trackProductMap
    );
  }
  
  return convertedData;
};

/**
 * 转换ETF配置数组
 * @param configArray ETF配置数组
 * @param trackProductMap 赛道产品映射
 * @returns 转换后的配置数组
 */
const convertETFConfigArray = (
  configArray: any[],
  trackProductMap: Record<string, SelectedETF[]>
): any[] => {
  return configArray.map(config => {
    const { selectedEtf, trackCode, etfCustomConfig, ...rest } = config;
    
    // 如果selectedEtf为空或trackCode不存在，返回原配置
    if (!selectedEtf || !trackCode || !trackProductMap[trackCode]) {
      return {
        ...rest,
        trackCode,
        selectedETF: [],
        selectedProductInfo: [],
      };
    }
    
    // 获取赛道产品列表
    const trackProducts = trackProductMap[trackCode];
    
    // 转换selectedEtf字符串数组为SelectedETF对象数组
    const selectedETFObjects: SelectedETF[] = selectedEtf
      .map((code: string) => {
        const baseETF = trackProducts.find(item => item.code === code);
        if (!baseETF) {
          console.warn(`未找到代码为 ${code} 的ETF产品`);
          return null;
        }
        
        // 合并自定义配置
        const customConfig = etfCustomConfig?.[code] || {};
        return {
          ...baseETF,
          customName: customConfig.customName,
          customHighlight: customConfig.customHighlight,
        };
      })
      .filter(Boolean) as SelectedETF[];
    
    return {
      ...rest,
      trackCode,
      selectedETF: selectedETFObjects,
      // 生成 selectedProductInfo 字段，存储选中产品的完整信息
      selectedProductInfo: selectedETFObjects,
    };
  });
};

/**
 * 将保存的数据转换为表单编辑所需的格式
 * @param savedData 保存的OpportunityItem数据
 * @returns 转换后的表单数据
 */
export const convertDataForEdit = (savedData: OpportunityItem): Partial<OpportunityItem> => {
  if (!savedData) {
    throw new Error('保存的数据不能为空');
  }
  
  // 深拷贝避免修改原数据
  const editData = JSON.parse(JSON.stringify(savedData));
  
  // 转换概念板块：从Block对象转换为code字符串
  if (editData.data?.conceptBlock && typeof editData.data.conceptBlock === 'object') {
    editData.data.conceptBlock = editData.data.conceptBlock.code;
  }
  
  // 转换AB测试值：从逗号分隔字符串转换为数组
  if (editData.data?.abValue && typeof editData.data.abValue === 'string') {
    editData.data.abValue = editData.data.abValue.split(',');
  }
  
  // 转换ETF配置
  if (editData.data?.fundConfig) {
    // 转换场内配置
    if (editData.data.fundConfig.inside) {
      editData.data.fundConfig.inside = convertETFConfigForEdit(editData.data.fundConfig.inside);
    }
    
    // 转换场外配置
    if (editData.data.fundConfig.outside) {
      editData.data.fundConfig.outside = convertETFConfigForEdit(editData.data.fundConfig.outside);
    }
  }
  
  // 产业链数据：现在字段名已统一，无需转换映射
  if (editData.data?.industryConfig?.detail) {
    console.log('产业链详情数据:', editData.data.industryConfig.detail);
  }
  
  return editData;
};

/**
 * 将ETF配置从保存格式转换为表单编辑格式
 * @param configArray ETF配置数组（保存格式）
 * @returns 转换后的配置数组（表单格式）
 */
const convertETFConfigForEdit = (configArray: any[]): any[] => {
  return configArray.map(config => {
    const { selectedEtf, etfCustomConfig, ...rest } = config;
    
    // 如果没有selectedEtf，返回默认配置
    if (!selectedEtf || !Array.isArray(selectedEtf)) {
      return {
        ...rest,
        selectedEtf: [],
        etfCustomConfig: {},
      };
    }
    
    // 直接返回原有的selectedEtf数组和etfCustomConfig对象
    return {
      ...rest,
      selectedEtf,
      etfCustomConfig: etfCustomConfig || {},
    };
  });
};

/**
 * 从OpportunityItem记录中提取AI生成的原始参数
 * @param record 机会记录项
 * @returns 提取的AI参数对象
 */
export const extractAiParamsFromRecord = (record: OpportunityItem): {
  name?: string;
  aiId?: string;
  newsContent?: string;
  targetIndustry?: string;
  initialIndustryChain?: string;
  configId?: number;
} => {
  try {
    const result = {
      name: record.name || '',
      aiId: '15262', // 默认AI标识ID
      newsContent: '',
      targetIndustry: '',
      initialIndustryChain: '上游：【】；中游：【】；下游：【】；',
      configId: record.configId,
    };

    // 尝试从aiInfo.prompt中解析原始参数
    if ((record as any).aiInfo?.prompt) {
      try {
        const promptData = JSON.parse((record as any).aiInfo.prompt);
        
        // 提取新闻内容
        if (promptData.query) {
          result.newsContent = promptData.query;
        }
        
        // 提取目标行业
        if (promptData.industry) {
          result.targetIndustry = promptData.industry;
        }
        
        // 提取初始产业链
        if (promptData.stream) {
          result.initialIndustryChain = promptData.stream;
        }
      } catch (parseError) {
        console.warn('解析AI参数失败，使用默认值:', parseError);
      }
    }

    // 从aiInfo中提取AI标识ID
    if ((record as any).aiInfo?.id) {
      result.aiId = (record as any).aiInfo.id;
    }

    return result;
  } catch (error) {
    console.error('提取AI参数时发生错误:', error);
    
    // 返回默认值
    return {
      name: record?.name || '',
      aiId: '15262',
      newsContent: '',
      targetIndustry: '',
      initialIndustryChain: '上游：【】；中游：【】；下游：【】；',
    };
  }
};

/**
 * 创建防抖函数
 * @param func 需要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @param immediate 是否立即执行第一次调用
 * @returns 包装后的防抖函数
 */
export const createDebounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number = 300,
  immediate: boolean = false
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout | null = null;
  let isFirstCall = true;

  return function (...args: Parameters<T>) {
    const callNow = immediate && isFirstCall;
    
    // 清除之前的定时器
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    if (callNow) {
      func(...args);
      isFirstCall = false;
    } else {
      timeoutId = setTimeout(() => {
        timeoutId = null;
        if (!immediate) {
          func(...args);
        }
        isFirstCall = true;
      }, delay);
    }
  };
};

/**
 * 创建表单提交防抖函数（专门针对异步提交场景）
 * @param func 异步提交函数
 * @param delay 防抖延迟时间（毫秒）
 * @returns 包装后的防抖提交函数，返回loading状态控制
 */
export const createSubmitDebounce = <T extends (...args: any[]) => Promise<any>>(
  func: T,
  delay: number = 1000
) => {
  let isSubmitting = false;
  let timeoutId: NodeJS.Timeout | null = null;

  return {
    // 防抖提交函数
    submit: async (...args: Parameters<T>): Promise<{ success: boolean; data?: any; error?: any }> => {
      // 如果正在提交中，直接返回
      if (isSubmitting) {
        console.warn('表单正在提交中，请勿重复操作');
        return { success: false, error: '请勿重复提交' };
      }

      // 清除之前的定时器
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      // 设置防抖定时器
      return new Promise((resolve) => {
        timeoutId = setTimeout(async () => {
          isSubmitting = true;
          timeoutId = null;

          try {
            const result = await func(...args);
            resolve({ success: true, data: result });
          } catch (error) {
            console.error('表单提交失败:', error);
            resolve({ success: false, error });
          } finally {
            isSubmitting = false;
          }
        }, delay);
      });
    },

    // 获取当前提交状态
    isSubmitting: () => isSubmitting,

    // 取消防抖提交
    cancel: () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
      isSubmitting = false;
    },

    // 重置状态
    reset: () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
      isSubmitting = false;
    }
  };
};

/**
 * 过滤数组中的null和undefined值
 * @param arr 待清理的数组
 * @returns 清理后的数组
 */
export const cleanNullFromArray = <T>(arr: (T | null | undefined)[]): T[] => {
  if (!Array.isArray(arr)) {
    console.warn('cleanNullFromArray: 输入参数不是数组', arr);
    return [];
  }
  
  try {
    return arr.filter((item): item is T => item !== null && item !== undefined);
  } catch (error) {
    console.error('cleanNullFromArray: 数组清理时发生错误', error);
    return [];
  }
};

/**
 * 清理基金配置数据中的null值
 * @param formData 表单数据对象
 * @returns 清理后的数据对象
 */
export const cleanFundConfigData = (formData: any): any => {
  if (!formData || typeof formData !== 'object') {
    console.warn('cleanFundConfigData: 输入数据无效', formData);
    return formData;
  }

  try {
    // 深拷贝避免修改原数据
    const cleanedData = JSON.parse(JSON.stringify(formData));

    // 清理场内基金配置
    if (cleanedData.data?.fundConfig?.inside && Array.isArray(cleanedData.data.fundConfig.inside)) {
      cleanedData.data.fundConfig.inside = cleanNullFromArray(cleanedData.data.fundConfig.inside);
    }

    // 清理场外基金配置
    if (cleanedData.data?.fundConfig?.outside && Array.isArray(cleanedData.data.fundConfig.outside)) {
      cleanedData.data.fundConfig.outside = cleanNullFromArray(cleanedData.data.fundConfig.outside);
    }

    // 清理产业链配置中的空项
    if (cleanedData.data?.industryConfig?.detail) {
      const industryDetail = cleanedData.data.industryConfig.detail;

      // 清理上游产业链数组
      if (industryDetail.upstream?.industry && Array.isArray(industryDetail.upstream.industry)) {
        industryDetail.upstream.industry = cleanNullFromArray(industryDetail.upstream.industry);
      }

      // 清理中游产业链数组
      if (industryDetail.midstream?.industry && Array.isArray(industryDetail.midstream.industry)) {
        industryDetail.midstream.industry = cleanNullFromArray(industryDetail.midstream.industry);
      }

      // 清理下游产业链数组
      if (industryDetail.downstream?.industry && Array.isArray(industryDetail.downstream.industry)) {
        industryDetail.downstream.industry = cleanNullFromArray(industryDetail.downstream.industry);
      }
    }

    return cleanedData;
  } catch (error) {
    console.error('cleanFundConfigData: 数据清理时发生错误', error);
    // 降级处理：返回原数据
    return formData;
  }
};