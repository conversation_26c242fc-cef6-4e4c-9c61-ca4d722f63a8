{"type": "page", "title": "ETF大赛券商配置", "body": [{"type": "form", "title": "券商列表", "body": [{"label": "", "type": "group", "name": "text", "id": "u:1cf9f1d309e7", "body": [{"type": "combo", "label": "", "name": "brokerList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:1df1957952a8"}, "items": [{"type": "input-text", "name": "brokerName", "id": "u:2c8c502d10ad", "label": "券商名称", "required": true, "mode": "horizontal", "size": "full"}, {"type": "input-text", "label": "券商ID", "name": "brokerId", "keyboard": true, "id": "u:4dbe9a3c9e5d", "required": true, "mode": "horizontal"}, {"type": "input-text", "label": "简介", "name": "brokerDesc", "id": "u:9b4a1baef811", "required": true, "validations": {"maxLength": 26}, "mode": "horizontal", "size": "full"}, {"type": "input-number", "label": "排序权重", "name": "sortWeight", "keyboard": true, "id": "u:fa3d5ea2c4ff", "step": 1, "mode": "horizontal", "required": true, "value": 0}, {"type": "select", "label": "是否支持换绑", "name": "supportModifyAccount", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "id": "u:253aaced437a", "multiple": false, "mode": "horizontal", "value": false, "required": true}, {"type": "input-text", "label": "绑定链接", "name": "bindAccountUrl", "id": "u:c16374bbd2f4", "mode": "horizontal", "required": true}, {"type": "input-image", "label": "券商Logo（144*144）", "name": "icon", "autoUpload": true, "proxy": true, "uploadType": "fileReceptor", "imageClassName": "r w-full", "id": "u:b29f929c3df5", "accept": ".jpeg, .jpg, .png", "multiple": false, "hideUploadButton": false, "fixedSize": false, "mode": "horizontal", "required": true, "receiver": {"url": "/commonservice/ceph/s3/uploadFile", "adaptor": "let _url = '';\r\nif (window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {\r\n  _url = 'https://testo.thsi.cn/' + payload.data\r\n} else {\r\n  _url = 'https://o.thsi.cn/' + payload.data;\r\n}\r\nreturn {\r\n  ...payload,\r\n  data: {\r\n    url: _url\r\n  }\r\n}"}, "limit": {"height": 144, "width": 144}}], "id": "u:19c215a194ad", "strictMode": true, "syncFields": [], "multiLine": true, "value": "${config}"}]}], "id": "u:c5dfe803e683", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "brokerList", "value": "${ENCODEJSON(brokerList)}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "submitText": "保存", "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "brokerList"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}}], "regions": ["body", "header"], "id": "u:a37f714f7e7e"}