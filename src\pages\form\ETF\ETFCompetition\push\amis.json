{"type": "page", "title": "ETF大赛推送配置", "body": [{"type": "form", "title": "推送配置", "body": [{"type": "combo", "label": "", "name": "pushList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:b615a96d7747"}, "items": [{"type": "input-text", "name": "name", "id": "u:582896be78a9", "label": "名称", "mode": "horizontal", "required": true, "unique": true}, {"type": "input-datetime-range", "name": "validTime", "placeholder": "请选择日期时间范围", "id": "u:21addd91c1ad", "label": "日期范围", "mode": "horizontal", "inputFormat": "YYYY-MM-DD HH:mm:ss", "format": "X", "minDate": "", "maxDate": "", "value": "", "ranges": [], "hidden": false, "required": true}, {"type": "input-text", "label": "推送标题", "name": "title", "id": "u:136669ea1f27", "mode": "horizontal", "required": true}, {"type": "input-text", "label": "推送内容", "name": "content", "id": "u:136669ea1f28", "mode": "horizontal", "required": true}, {"type": "input-text", "label": "跳转链接", "name": "jumpUrl", "id": "u:136669ea1f29", "mode": "horizontal", "required": true}, {"type": "select", "label": "人群类型", "name": "userType", "options": [{"label": "已报名", "value": 1}, {"label": "完成报名未绑定", "value": 2}, {"label": "完成绑定未交易", "value": 3}, {"label": "完成报名有自选", "value": 4}, {"label": "有现金奖励", "value": 5}], "id": "u:68934c578318", "multiple": false, "mode": "horizontal", "required": true, "value": 0}], "id": "u:0d1fbf05d5f8", "strictMode": true, "syncFields": [], "multiLine": true, "value": "${config}"}], "id": "u:3ec481ba144d", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "push", "value": "${ENCODEJSON(pushList)}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "push"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}, "submitText": "保存"}], "regions": ["body", "header"], "id": "u:dc588a95fe28"}