{"type": "page", "title": "ETF大赛通用配置", "body": [{"type": "form", "title": "活动配置", "body": [{"label": "活跃活动ID列表", "type": "input-text", "name": "competitionActivityIdList", "id": "u:02f932066c1e", "mode": "horizontal", "value": "${config}"}], "id": "u:50193e30c0fd", "submitText": "保存", "api": {"url": "/common_config/kv_data_save", "method": "post", "messages": {}, "data": {"key": "competitionActivityIdList", "value": "${competitionActivityIdList}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "initApi": {"url": "/common_config/kv_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "competitionActivityIdList"}, "adaptor": "const config = payload.data;\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}}, {"type": "form", "title": "配置导入", "body": [{"type": "select", "label": "配置类型", "name": "propName", "options": [{"label": "凭证配置", "value": "voucherList"}, {"label": "抽奖配置", "value": "drawConfig"}, {"label": "事件配置", "value": "actionConfig"}, {"label": "瓜分配置", "value": "carveUpList"}, {"label": "榜单奖励配置", "value": "rankAward"}, {"label": "黑名单配置", "value": "blackList"}, {"label": "券商配置", "value": "brokerList"}, {"label": "圈子配置", "value": "bulletComment"}, {"label": "赛事宝典配置", "value": "competitionBible"}, {"label": "大赛信息配置", "value": "info"}, {"label": "大赛ETF池配置", "value": "etfPool"}, {"label": "基金公司配置", "value": "companyList"}, {"label": "大赛主会场配置", "value": "mainVenue"}, {"label": "弹窗配置", "value": "popup"}, {"label": "公告配置", "value": "notice"}, {"label": "推送配置", "value": "push"}, {"label": "榜单配置", "value": "rankingBanner"}, {"label": "分享配置", "value": "share"}, {"label": "任务配置", "value": "taskList"}], "id": "u:febbdbd4f617"}, {"type": "input-text", "name": "fromActivityId", "label": "来源活动ID", "id": "u:8d6f5f2e157a"}, {"type": "input-text", "name": "toActivityId", "label": "目标活动ID", "id": "u:bd59d0cbf392"}, {"type": "textarea", "name": "config", "label": "配置详情", "id": "u:d97d5537443f", "value": "${config}"}], "mode": "horizontal", "id": "u:9729a9af6c5d", "api": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${fromActivityId}", "propName": "${propName}"}, "adaptor": "const config = payload.data;\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}, "actions": [{"type": "button", "label": "获取来源活动配置", "onEvent": {"click": {"actions": [{"componentId": "u:9729a9af6c5d", "args": {}, "actionType": "submit"}]}}, "id": "u:8d1b4be11182", "level": "primary"}, {"type": "button", "label": "保存配置至目标活动", "onEvent": {"click": {"actions": [{"args": {"options": {}, "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "etf_competition_activity_config_${toActivityId}", "propName": "${propName}", "value": "${config}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}}, "outputVar": "responseResult", "actionType": "ajax"}]}}, "id": "u:dbf9aec76e60", "level": "primary"}]}], "regions": ["body", "header"], "id": "u:4a5db4faab4a"}