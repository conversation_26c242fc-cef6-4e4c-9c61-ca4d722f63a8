import React, { useEffect, useState } from 'react';
import api from 'api';
import Activity from './activity';
import { Button, message, Drawer, Table } from 'antd';

const { getEtfEarningConfig, postEtfEarningConfig } = api;

interface ETFActivityItem {
  activityId: string;
  type: string;
  cardType: string;
  activityName: string;
  startDate: string;
  endDate: string;
  status: string;
  title: string;
  subTitle: string;
  tipTitle: string;
  tipContext: string;
  recommendTitle: string;
  recommendCode: string;
  recommendName: string;
  profitDescribe: string;
  productTitle: string;
  purchaseContext: string;
  windowText: string;
  activityRules: string | any;
  codeRate: string;
  cashVoucherList: any;
}

export default function() {
  const [activities, setActivities] = useState([] as ETFActivityItem[]); //列表
  const [isShowAdd, setIsShowAdd] = useState(false); //是否显示modal
  const [selectedActivity, setSelectedActivity] = useState({}); //选中的活动
  const [isAdd, setIsAdd] = useState(true); //是否为添加
  const [awardList, setAwardList] = useState([{}, {}, {}, {}]); //名单
  const columns = [
    {
      title: '活动id',
      dataIndex: 'activeId',
      key: 'activeId',
    },
    {
      title: '活动名称',
      dataIndex: 'activeName',
      key: 'activeName',
    },
    {
      title: '起始时间',
      dataIndex: 'beginTime',
      key: 'beginTime',
    },
    {
      title: '中止时间',
      dataIndex: 'endTime',
      key: 'endTime',
    },
    {
      title: '按钮',
      key: 'button',
      render: (item: any, record, index) => {
        return (
          <>
            <Button
              type="primary"
              style={{ marginRight: 20 }}
              onClick={() => {
                queryActivity(record);
              }}
            >
              查看全部
            </Button>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    // postEtfEarningConfig({
    // 	value: JSON.stringify([])
    // })
    handleFetchETFActivity();
  }, []);

  /**
   * 查询活动列表
   */

  function handleFetchETFActivity() {
    _.fundLoading();
    getEtfEarningConfig({ key: 'etf-earning-new' })
      .then((data: any) => {
        _.hideFundLoading();
				console.log(data.data, 'data.data');
        if (data.code === '0000') {
          if (!data.data) {
            return;
          }
          const list = Object.values(data.data).map(item => JSON.parse(item));
          console.log(list, 'list');
          setActivities(data.data ? list : []);
        } else {
          message.error(data.message);
        }
      })
      .catch(() => {
        _.hideFundLoading();
      });
  }

  /**
   * 查询单个
   * @param activityId
   */
  function queryActivity(record) {
    _.fundLoading();
    console.log(record, 'queryActivity');
    // record.activityRules = JSON.parse(record.activityRules)
    // record.recommend = JSON.parse(record.recommend)
    // record.product = JSON.parse(record.product)
    // record.award = JSON.parse(record.award)

    setSelectedActivity(record);
    setAwardList(record.award);
    setIsAdd(false);
    setIsShowAdd(true);
    _.hideFundLoading();
    // fetchETFActivity({
    //     type: 'query',
    //     activityId
    // }).then((data: any) => {
    //     _.hideFundLoading()
    //     if (data.code === '0000') {
    // data.data.activityRules = JSON.parse(data.data.activityRules)
    // if (data.data.codeRate) data.data.codeRate = Number(data.data.codeRate)
    // if (!data.data.exchangeCodeConfig) {
    //     data.data.exchangeCodeConfig = {};
    // }
    //         setSelectedActivity(data.data)
    //         setIsAdd(false)
    //         setIsShowAdd(true)
    //     } else {
    //         message.error(data.message)
    //     }
    // }).catch(() => {
    //     _.hideFundLoading()
    // })
  }

  function showAdd() {
    setSelectedActivity({});
    setIsAdd(true);
    setIsShowAdd(true);
  }

  return (
    <div>
      <Table columns={columns} dataSource={activities}></Table>
      <Button type="primary" style={{ marginTop: '20px' }} onClick={showAdd}>
        增加活动
      </Button>

      <Drawer
        title="增加活动"
        width={1200}
        onClose={() => {
          setIsShowAdd(false);
        }}
        visible={isShowAdd}
        bodyStyle={{ paddingBottom: 80 }}
      >
        {isShowAdd && (
          <Activity
            isAdd={isAdd}
            isShowAdd={isShowAdd}
            activities={activities}
            activity={selectedActivity}
            setActivity={setSelectedActivity}
            setIsShowAdd={setIsShowAdd}
            awardList={awardList}
            setAwardList={setAwardList}
            handleFetchETFActivity={handleFetchETFActivity}
          />
        )}
      </Drawer>
    </div>
  );
}
