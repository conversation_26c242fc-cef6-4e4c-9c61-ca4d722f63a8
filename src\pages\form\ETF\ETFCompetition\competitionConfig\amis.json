{"type": "page", "title": "ETF大赛信息配置", "body": [{"type": "form", "title": "大赛相关信息", "body": [{"type": "input-text", "label": "活动ID", "name": "activityId", "id": "u:7b910ca6ead3", "mode": "horizontal", "required": true, "value": "${config.activityId}"}, {"type": "input-text", "label": "安全部活动ID", "name": "securityId", "id": "u:7b910ca6ead4", "mode": "horizontal", "required": true, "value": "${config.securityId}"}, {"type": "input-datetime", "label": "报名开始时间", "name": "signStartTime", "id": "u:eeceff72cb72", "size": "lg", "mode": "horizontal", "inputFormat": "YYYYMMDDHHmmss", "placeholder": "请选择日期以及时间", "format": "YYYYMMDDHHmmss", "minDate": "", "maxDate": "", "value": "${config.signStartTime}", "required": true}, {"type": "input-datetime", "label": "报名结束时间", "name": "signEndTime", "id": "u:b18b31bfe5e3", "mode": "horizontal", "size": "lg", "required": true, "inputFormat": "YYYYMMDDHHmmss", "placeholder": "请选择日期以及时间", "format": "YYYYMMDDHHmmss", "minDate": "", "maxDate": "", "value": "${config.signEndTime}"}, {"type": "input-datetime", "label": "比赛开始时间", "name": "competitionStartTime", "id": "u:eed042727e38", "inputFormat": "YYYYMMDDHHmmss", "placeholder": "请选择日期以及时间", "format": "YYYYMMDDHHmmss", "minDate": "", "maxDate": "", "value": "${config.competitionStartTime}", "required": true, "mode": "horizontal", "size": "lg"}, {"type": "input-datetime", "label": "比赛结束时间", "name": "competitionEndTime", "id": "u:c028ce71c06c", "mode": "horizontal", "size": "lg", "inputFormat": "YYYYMMDDHHmmss", "placeholder": "请选择日期以及时间", "format": "YYYYMMDDHHmmss", "minDate": "", "maxDate": "", "value": "${config.competitionEndTime}", "required": true}, {"type": "input-datetime", "label": "领奖截止时间", "name": "receiveEndTime", "id": "u:288f3fe4e758", "mode": "horizontal", "inputFormat": "YYYYMMDDHHmmss", "placeholder": "请选择日期以及时间", "format": "YYYYMMDDHHmmss", "minDate": "", "maxDate": "", "value": "${config.receiveEndTime}", "required": true, "size": "lg"}, {"type": "input-number", "label": "报名累加基数", "name": "signBaseNum", "keyboard": true, "id": "u:c178f0720b27", "step": 1, "mode": "horizontal", "required": true, "value": "${config.signBaseNum}"}, {"type": "input-number", "label": "报名系数", "name": "signRatio", "keyboard": true, "id": "u:0edf1a4a03b7", "step": 1, "mode": "horizontal", "required": true, "value": "${config.signRatio}"}, {"type": "input-url", "label": "比赛规则链接", "name": "ruleUrl", "id": "u:55228a554002", "mode": "horizontal", "required": true, "validations": {}, "validationErrors": {}, "value": "${config.ruleUrl}"}, {"type": "input-url", "label": "任务说明链接", "name": "taskUrl", "id": "u:e25aa8250d5c", "mode": "horizontal", "required": true, "validations": {}, "validationErrors": {}, "value": "${config.taskUrl}"}, {"type": "input-url", "label": "奖励说明链接", "name": "awardUrl", "id": "u:7d1124ab9bcd", "required": true, "mode": "horizontal", "value": "${config.awardUrl}"}], "id": "u:da2ee3ae6024", "submitText": "保存", "mode": "normal", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "info", "value": "{\"activityId\": \"${activityId}\",\"securityId\": \"${securityId}\", \"signStartTime\": \"${signStartTime}\", \"signEndTime\": \"${signEndTime}\", \"competitionStartTime\": \"${competitionStartTime}\", \"competitionEndTime\": \"${competitionEndTime}\", \"receiveEndTime\": \"${receiveEndTime}\", \"signBaseNum\": ${signBaseNum}, \"signRatio\": ${signRatio}, \"ruleUrl\": \"${ruleUrl}\", \"taskUrl\": \"${taskUrl}\", \"awardUrl\": \"${awardUrl}\"}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "info"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}}], "regions": ["body", "header"], "id": "u:1329990c527a"}