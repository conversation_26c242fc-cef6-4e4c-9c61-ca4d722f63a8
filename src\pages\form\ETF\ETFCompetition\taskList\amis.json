{"type": "page", "title": "ETF大赛任务配置", "body": [{"type": "form", "title": "任务", "body": [{"type": "combo", "id": "u:c54b6a45e85f", "label": "", "name": "taskList", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:24f019de46e1"}, "items": [{"type": "input-text", "label": "任务ID", "name": "taskId", "id": "u:0ed2d3faf5b3", "mode": "horizontal", "required": true}, {"type": "input-text", "name": "taskName", "placeholder": "", "id": "u:3707c654184e", "label": "任务名称", "required": true, "mode": "horizontal", "value": ""}, {"type": "select", "name": "taskType", "options": [{"label": "跳转页面", "value": 0}, {"label": "加自选", "value": 1}, {"label": "购买场外基金", "value": 2}, {"label": "加圈子", "value": 3}, {"label": "关注同顺号", "value": 4}, {"label": "加宫格", "value": 5}, {"label": "分享", "value": 6}], "id": "u:c484ad8bf32d", "multiple": false, "label": "任务类型", "required": true, "mode": "horizontal"}, {"type": "input-text", "label": "任务分组", "name": "taskGroup", "id": "u:0f8a13dab14b", "required": true, "mode": "horizontal", "placeholder": "", "value": ""}, {"type": "input-text", "label": "任务跳转链接", "name": "taskUrl", "id": "u:82a12ed0414e", "mode": "horizontal", "required": true}, {"type": "input-text", "label": "完成条件描述", "name": "taskConditionDesc", "id": "u:a07301df8207", "mode": "horizontal", "required": true}, {"type": "select", "label": "条件类型", "name": "conditionType", "id": "u:d149b72f4792", "required": true, "mode": "horizontal", "options": [{"label": "外部触发", "value": "EXTERNAL_TRIGGER"}, {"label": "抽奖次数", "value": "DRAW_COUNT"}, {"label": "基金开户", "value": "FUND_ACCOUNT"}, {"label": "比赛交易一笔", "value": "COMPETITION_TRADE"}, {"label": "用户收益率", "value": "USER_RATE"}, {"label": "用户报名", "value": "USER_SIGN"}, {"label": "基金交易", "value": "FUND_TRADE"}], "multiple": false, "value": "EXTERNAL_TRIGGER"}, {"draggable": true, "type": "input-kv", "label": "条件参数", "name": "conditionParam", "id": "u:8d6c094947ba", "multiple": true, "items": [{"placeholder": "Key", "type": "input-text", "unique": true, "name": "key", "required": true, "validateOnChange": true}, {"placeholder": "Value", "type": "input-text", "name": "value"}], "mode": "horizontal"}, {"type": "select", "label": "凭证ID", "name": "voucherId", "id": "u:8f6a8944093e", "multiple": false, "required": true, "mode": "horizontal", "source": "${voucherList}", "labelField": "voucherDesc", "valueField": "voucherId"}, {"type": "input-text", "label": "凭证数量", "name": "voucherAmount", "id": "u:2debb676836e", "mode": "horizontal", "required": true}], "strictMode": true, "syncFields": [], "multiLine": true, "value": "${config}"}], "id": "u:129761e7bafc", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "taskList", "value": "${ENCODEJSON(taskList)}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "taskList"}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    config: config,\r\n  },\r\n};"}, "submitText": "保存"}], "regions": ["body", "header"], "id": "u:b90dc6c54b42"}