{"type": "page", "title": "ETF大赛榜单审核发奖", "body": [{"type": "form", "id": "u:f7eca0fff1bb", "title": "券商数据回传情况查询", "body": [{"type": "select", "label": "券商", "name": "appkey", "options": [{"label": "东兴", "value": "dxzq"}, {"label": "浙商", "value": "zszq"}, {"label": "万和", "value": "wanhesec_etf_wjh7e7"}, {"label": "太平洋", "value": "tpyzq2023"}, {"label": "中信建投", "value": "zxjtzq2023"}, {"label": "国信", "value": "gxzq2023"}, {"label": "平安", "value": "pazq2023"}, {"label": "安信", "value": "axzq2023"}, {"label": "国金", "value": "gjzq2023"}, {"label": "兴业", "value": "xyzq2023"}, {"label": "华创", "value": "hczq2023"}, {"label": "开源", "value": "kyzq2023"}, {"label": "长城", "value": "cczq2023"}, {"label": "国元", "value": "gyzq2023"}], "id": "u:2dbe799cac21", "multiple": false, "required": true}, {"type": "textarea", "id": "u:1f2b426b2176", "label": "查询结果", "name": "data", "minRows": 3, "maxRows": 20, "value": "${result}"}], "initApi": {"url": "${fundPrefix}marketing/activity/component/user/v1/list_lose_broker/${activityId}", "method": "get", "messages": {}, "adaptor": "return {\r\n  status: payload.status_code,\r\n  msg: payload.status_msg,\r\n  data: {\r\n    result: payload.data\r\n  }\r\n}"}, "submitText": "查询", "api": {"url": "${fundPrefix}marketing/activity/component/user/v1/list_lose", "method": "post", "messages": {}, "data": {"appkey": "${appkey}"}, "adaptor": "return {\r\n  status: payload.status_code,\r\n  msg: payload.status_msg,\r\n  data: {\r\n    result: payload.data\r\n  }\r\n}"}, "onEvent": {}, "mode": "horizontal"}, {"type": "crud", "id": "u:a3251ca189fd", "api": {"url": "${fundPrefix}marketing/firm_competition/rank/v1/user_rank", "method": "get", "data": {"activity": "${activityId}", "type": "${typeData.type}", "limit": 500}}, "syncLocation": false, "headerToolbar": [{"type": "export-excel", "filename": "${typeData.type}", "api": {"url": "${fundPrefix}marketing/firm_competition/rank/v1/user_rank", "method": "get", "data": {"activity": "${activityId}", "type": "${typeData.type}", "limit": 500}}, "id": "a3251ca189fg"}, {"type": "bulk-actions", "tpl": "内容", "wrapperComponent": "", "id": "u:72a634c20d06"}], "columns": [{"name": "rank", "label": "排名", "type": "text", "id": "u:b6ae78e95547"}, {"name": "userId", "label": "用户ID", "type": "text", "id": "u:f64455f1ea80"}, {"type": "text", "label": "加密用户ID", "name": "aesUserId", "id": "u:2d94b0c5b798"}, {"type": "text", "label": "用户名", "name": "name", "id": "u:d719ef44eab3"}, {"type": "text", "label": "券商", "name": "brokerId", "id": "u:2d94b0c5b798"}, {"type": "text", "label": "收益率", "name": "earning", "id": "u:2d94b0c5b797"}, {"type": "tpl", "label": "奖励", "tpl": "<% if(!data.typeData.name.startsWith('第')) { return; } var configData = []; if(data.typeData.type.startsWith('all_')){ configData = data.rankAward?.totalConfig; }else if(data.typeData.type.startsWith('month_')){ configData = data.rankAward?.monthConfig; }else if(data.typeData.type.startsWith('week_')){ configData = data.rankAward?.weekConfig; } configData?.forEach((rankConfig, index) => { if(data.rank >= rankConfig.startIndex && data.rank <= rankConfig.endIndex) {%> <%= JSON.stringify(rankConfig.rightsList) %> <% }}); %>", "id": "u:2d94b0c5b798"}], "bulkActions": [{"type": "button", "label": "发奖", "id": "u:24f24b963734", "confirmText": "确认后将针对选中的${selectedItems.length}个用户发奖，是否确认发奖", "disabledOn": "${!STARTSWITH(typeData.name, '第')}", "onEvent": {"click": {"weight": 0, "actions": [{"args": {"options": {}, "api": {"url": "/firm_competition/provide_list", "method": "post", "data": {"transferId": "${typeData.type}", "activityId": "${activityId}", "userName": "${ls:name}", "rankList": "${selectedItems}", "rankAward": "${rankAward}", "typeData": "${typeData}"}, "adaptor": "return {\r\n  status: payload.status_code,\r\n  msg: payload.status_msg,\r\n}", "messages": {}, "requestAdaptor": "api.data['provideList'] = [];\r\nvar configData = []\r\nif (api.data.typeData.type.startsWith('all_')) {\r\n  configData = api.data.rankAward.totalConfig;\r\n} else if (api.data.typeData.type.startsWith('month_')) {\r\n  configData = api.data.rankAward.monthConfig;\r\n} else if (api.data.typeData.type.startsWith('week_')) {\r\n  configData = api.data.rankAward.weekConfig;\r\n}\r\n\r\napi.data.rankList.forEach((user, index) => {\r\n  var userData = {};\r\n  userData['userId'] = user.userId;\r\n  userData['rightsList'] = [];\r\n  configData.forEach((rankConfig, index) => {\r\n    if (user.rank >= rankConfig.startIndex && user.rank <= rankConfig.endIndex) {\r\n      rankConfig.rightsList.forEach((rights, index) => {\r\n        var rightsData = {};\r\n        rightsData['rightsId'] = rights.rightsData.rightsId;\r\n        rightsData['target'] = rights.rightsData.target;\r\n        rightsData['rightsDesc'] = rights.rightsData.rightsDesc;\r\n        rightsData['count'] = rights.count;\r\n        userData.rightsList.push(rightsData);\r\n      });\r\n    }\r\n  });\r\n\r\n  if (userData.rightsList.length > 0) {\r\n    api.data.provideList.push(userData);\r\n  }\r\n});\r\napi.data.rankList = undefined;\r\napi.data.rankAward = undefined;\r\napi.data.typeData = undefined;\r\n\r\n"}}, "actionType": "ajax", "outputVar": "responseResult"}]}}, "level": "danger"}], "itemActions": [], "perPageAvailable": [10], "messages": {}, "filter": {"title": "查询条件", "body": [{"type": "tree-select", "label": "榜单选择", "name": "typeData", "id": "u:7f80f1827a31", "multiple": false, "enableNodePath": false, "hideRoot": true, "showIcon": false, "initiallyOpen": true, "onEvent": {}, "onlyLeaf": true, "hideNodePathLabel": true, "source": {"url": "${fundPrefix}marketing/firm_competition/rank/v1/rank_type", "method": "get", "messages": {}, "data": {"activity": "${activityId}", "status": "review"}, "adaptor": "if (payload.data && Array.isArray(payload.data)) {\r\n  payload.data.forEach((rank, index) => {\r\n    rank['data'] = {};\r\n    rank.data['type'] = rank['type'];\r\n    rank.data['name'] = rank['name'];\r\n    rank.data['begin'] = rank['begin'];\r\n    rank.data['end'] = rank['end'];\r\n    rank['children'] = rank.rankKeyInfoList;\r\n    if (Array.isArray(rank.children)) {\r\n      rank.children.forEach((child, index) => {\r\n        child['data'] = {};\r\n        child.data['type'] = child['type'];\r\n        child.data['name'] = child['name'];\r\n        child.data['begin'] = child['begin'];\r\n        child.data['end'] = child['end'];\r\n      })\r\n    }\r\n  })\r\n}\r\nreturn payload;"}, "size": "lg", "labelField": "name", "valueField": "data"}], "id": "u:1744da463acc", "submitText": "查询", "actions": [{"type": "button", "label": "查询", "onEvent": {"click": {"actions": [{"componentId": "u:1744da463acc", "args": {}, "actionType": "submit"}, {"args": {"options": {}, "api": {"url": "/firm_competition/check_provide_list", "method": "get", "messages": {}, "data": {"transferId": "${typeData.type}"}, "adaptor": "var message = payload.status_msg;\r\nif (payload.status_code === 0 && payload.data.checkResult === '0') {\r\n  message = '该榜单已发奖';\r\n}\r\n\r\nreturn {\r\n  status: payload.status_code,\r\n  msg: message,\r\n  data: payload.data,\r\n}"}}, "outputVar": "responseResult", "actionType": "ajax"}]}}, "id": "u:5abed508223a", "level": "primary"}, {"type": "button", "label": "榜单审核通过", "onEvent": {"click": {"actions": [{"args": {"options": {}, "api": {"url": "/common_config/kv_data_save", "method": "post", "messages": {}, "data": {"key": "firm_competition:review:${activityId}:user_rank_date", "value": "${SPLIT(typeData.type, '_')[1]}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}}, "outputVar": "responseResult", "actionType": "ajax"}]}}, "id": "u:e261d5d3f5d5", "confirmText": "确认后表示${SPLIT(typeData.type, '_')[1]}所更新的日榜、周榜、月榜均审核通过公布给用户，是否确认通过？", "level": "primary", "disabledOn": "${!STARTSWITH(typeData.type, 'today_')}"}]}, "keepItemSelectionOnPageChange": true, "loadDataOnce": true, "alwaysShowPagination": true, "perPage": 100, "pageField": "page", "initFetch": false, "primaryField": "rank"}, {"type": "form", "title": "手动发奖", "body": [{"type": "input-excel", "label": "奖励名单", "name": "userList", "id": "u:f1b0bae1b26b", "parseMode": "object", "allSheets": true}], "id": "u:6c15ffa0cd82", "actions": [{"type": "button", "label": "发奖", "onEvent": {"click": {"actions": [{"componentId": "u:6c15ffa0cd82", "args": {}, "actionType": "submit"}]}}, "id": "u:aeb5d3c02dfe", "level": "danger", "confirmText": "确认后将针对上传的${userList[0].data.length}个用户发奖，是否确认发奖", "disabledOn": "${!userList || userList.length <= 0 || userList[0].data.length <= 0}"}], "debug": true, "api": {"url": "/firm_competition/provide_list", "method": "post", "messages": {}, "data": {"transferId": "${userList[0].sheetName}", "activityId": "${activityId}", "userName": "${ls:name}", "userList": "${userList[0].data}"}, "adaptor": "return {\r\n  status: payload.status_code,\r\n  msg: payload.status_msg,\r\n}", "requestAdaptor": "api.data['provideList'] = [];\r\napi.data.userList.forEach((user, index) => {\r\n  var userData = {};\r\n  userData['userId'] = user.userId;\r\n  userData['rightsList'] = [];\r\n  var rightsData = {};\r\n  rightsData['rightsId'] = user.rightsId;\r\n  rightsData['target'] = user.target;\r\n  rightsData['rightsDesc'] = user.rightsDesc;\r\n  rightsData['count'] = user.count;\r\n  userData.rightsList.push(rightsData);\r\n  api.data.provideList.push(userData);\r\n});\r\n\r\napi.data.userList = undefined;"}}, {"type": "form", "title": "奖励提现状态回滚", "body": [{"label": "用户ID", "type": "input-text", "name": "userId", "id": "u:eb13808cd997", "required": true}], "id": "u:39ea86cc5620", "submitText": "提交", "mode": "horizontal", "api": {"url": "${khPrefix}assembly/center/rights/center/fund_share/rollback/v1", "method": "post", "messages": {}, "data": {"activityId": "${activityId}", "userId": "${userId}", "userType": 1}, "headers": {"token": "manta"}}}], "id": "u:0b8f9cb113f7", "initApi": {"url": "/common_config/hash_data_get", "method": "get", "messages": {}, "adaptor": "const config = JSON.parse(payload.data);\r\nreturn {\r\n  data: {\r\n    rankAward: config,\r\n  },\r\n};", "dataType": "form", "data": {"key": "etf_competition_activity_config_${activityId}", "propName": "rankAward"}}, "regions": ["body", "header"]}